/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for theming */
:root {
    /* Dark theme (default) */
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --bg-quaternary: #4a4a4a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-color: #333333;
    --accent-color: #4a9eff;
    --accent-hover: #5aaeff;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --modal-bg: #1a1a1a;
    --input-bg: #2a2a2a;
    --button-bg: #4a4a4a;
    --button-hover: #5a5a5a;
    --success-color: #44ff44;
    --error-color: #ff4444;
    --info-color: #4444ff;
}

/* Light theme */
body[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-quaternary: #dee2e6;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --accent-color: #007bff;
    --accent-hover: #0056b3;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --modal-bg: #ffffff;
    --input-bg: #ffffff;
    --button-bg: #6c757d;
    --button-hover: #5a6268;
    --success-color: #28a745;
    --error-color: #dc3545;
    --info-color: #17a2b8;
}

/* Auto theme (follows system preference) */
@media (prefers-color-scheme: light) {
    body[data-theme="auto"] {
        --bg-primary: #ffffff;
        --bg-secondary: #f8f9fa;
        --bg-tertiary: #e9ecef;
        --bg-quaternary: #dee2e6;
        --text-primary: #212529;
        --text-secondary: #495057;
        --text-muted: #6c757d;
        --border-color: #dee2e6;
        --accent-color: #007bff;
        --accent-hover: #0056b3;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --modal-bg: #ffffff;
        --input-bg: #ffffff;
        --button-bg: #6c757d;
        --button-hover: #5a6268;
        --success-color: #28a745;
        --error-color: #dc3545;
        --info-color: #17a2b8;
    }
}

@media (prefers-color-scheme: dark) {
    body[data-theme="auto"] {
        --bg-primary: #0f0f0f;
        --bg-secondary: #1a1a1a;
        --bg-tertiary: #2a2a2a;
        --bg-quaternary: #4a4a4a;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --text-muted: #888888;
        --border-color: #333333;
        --accent-color: #4a9eff;
        --accent-hover: #5aaeff;
        --shadow-color: rgba(0, 0, 0, 0.3);
        --modal-bg: #1a1a1a;
        --input-bg: #2a2a2a;
        --button-bg: #4a4a4a;
        --button-hover: #5a5a5a;
        --success-color: #44ff44;
        --error-color: #ff4444;
        --info-color: #4444ff;
    }
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* RTL Support */
body[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

body[dir="rtl"] * {
    text-align: inherit;
}

/* Header styles */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 8px 16px;
    height: 60px;
    min-height: 60px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.header-left, .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 16px;
}

/* RTL Header adjustments */
body[dir="rtl"] .header-left {
    order: 3;
}

body[dir="rtl"] .header-center {
    order: 2;
}

body[dir="rtl"] .header-right {
    order: 1;
}

.url-bar {
    display: flex;
    align-items: center;
    background-color: var(--bg-tertiary);
    border-radius: 24px;
    padding: 4px;
    width: 100%;
    max-width: 500px;
    transition: background-color 0.3s ease;
}

#urlInput {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    padding: 8px 16px;
    font-size: 14px;
    outline: none;
    transition: color 0.3s ease;
}

#urlInput::placeholder {
    color: var(--text-muted);
}

.go-btn {
    background-color: var(--button-bg);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.go-btn:hover {
    background-color: var(--button-hover);
}

.control-btn {
    background-color: transparent;
    border: none;
    color: var(--text-primary);
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.control-btn:hover {
    background-color: var(--bg-quaternary);
}

.control-btn:active {
    background-color: #444;
}

/* Main content */
.main-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#webview {
    width: 100%;
    height: 100%;
    border: none;
    background-color: #000;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: var(--shadow-color);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--modal-bg);
    border-radius: 12px;
    padding: 0;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px var(--shadow-color);
    animation: modalSlideIn 0.3s ease-out;
    transition: background-color 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.modal-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
}

.close-btn:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    max-height: 60vh;
}

.settings-section {
    margin-bottom: 32px;
}

.settings-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    transition: color 0.3s ease;
}

.quality-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 12px;
}

.quality-option {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: var(--bg-tertiary);
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    transition: background-color 0.2s;
    border: 2px solid transparent;
}

.quality-option:hover {
    background-color: #3a3a3a;
}

.quality-option input[type="radio"] {
    margin-right: 8px;
    accent-color: #ff0000;
}

.quality-option input[type="radio"]:checked + .quality-label {
    color: #ff0000;
    font-weight: 600;
}

.quality-label {
    color: var(--text-primary);
    font-size: 14px;
    transition: color 0.3s ease;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: var(--text-primary);
    font-size: 14px;
    transition: color 0.3s ease;
}

.setting-item input[type="checkbox"] {
    accent-color: #ff0000;
    width: 18px;
    height: 18px;
}

.setting-item input[type="range"] {
    width: 120px;
    margin: 0 12px;
}

.setting-item select {
    background-color: var(--input-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.modal-footer {
    display: flex;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    justify-content: flex-end;
    transition: border-color 0.3s ease;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--text-primary);
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
}

.btn-secondary {
    background-color: var(--button-bg);
    color: var(--text-primary);
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--button-hover);
}

/* Quality overlay */
.quality-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--shadow-color);
    z-index: 999;
    backdrop-filter: blur(4px);
}

.quality-overlay.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.quality-panel {
    background-color: #1a1a1a;
    border-radius: 12px;
    padding: 24px;
    position: relative;
    min-width: 300px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.quality-panel h3 {
    color: #ffffff;
    margin-bottom: 20px;
    text-align: center;
    font-size: 18px;
}

.quality-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.quality-btn {
    background-color: #2a2a2a;
    border: 2px solid transparent;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
}

.quality-btn:hover {
    background-color: #3a3a3a;
    border-color: #ff0000;
}

.quality-btn.active {
    background-color: #ff0000;
    border-color: #ff0000;
    color: #ffffff;
}

.close-quality-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    color: #888;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
}

.close-quality-btn:hover {
    color: #ffffff;
}

/* Loading indicator */
.loading-indicator {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 20px 30px;
    border-radius: 8px;
    z-index: 1001;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.loading-indicator.show {
    display: flex;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #333;
    border-top: 2px solid #ff0000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .header {
        padding: 8px 12px;
    }
    
    .header-center {
        margin: 0 8px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .quality-controls {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quality-buttons {
        grid-template-columns: 1fr;
    }
}

/* Scrollbar styling */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #555;
} 