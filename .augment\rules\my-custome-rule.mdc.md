---
alwaysApply: true
type: "always_apply"
---

# قوانين النموذج المثالي - Blackbox Rules

# هذه القوانين مصممة لتحسين أداء النموذج وجعله بيرفكت

## 1. قوانين الكود والبرمجة

- **جودة الكود**: اكتب كود نظيف، قابل للقراءة، مع تعليقات واضحة
- **أفضل الممارسات**: اتبع أفضل ممارسات اللغة المستخدمة (ES6+ للـ JavaScript)
- **الاختبارات**: اكتب اختبارات وحدة لكل وظيفة رئيسية
- **الأداء**: استخدم خوارزميات فعالة مع تعقيد زمني O(n log n) أو أفضل
- **الأمان**: تحقق من صحة جميع المدخلات ومنع هجمات XSS وSQL injection

## 2. قوانين تصميم واجهة المستخدم

- **تجربة المستخدم**: تصميم بديهي مع تدفق منطقي للمستخدم
- **التجاوب**: تصميم متجاوب يعمل على جميع الأحجام (mobile-first)
- **الأداء**: تحميل سريع مع lazy loading للصور والمكونات
- **الإمكانية**: دعم كامل لـ ARIA وإمكانية الوصول
- **الألوان**: استخدام نظام ألوان متناسق مع نسب تباين 4.5:1 على الأقل

## 3. قوانين إدارة الحالة

- **الحالة العالمية**: استخدام Redux أو Zustand للحالة المعقدة
- **الحالة المحلية**: استخدام React hooks للحالة البسيطة
- **الكاش**: تنفيذ استراتيجية كاش فعالة مع invalidation مناسب
- **المزامنة**: مزامنة البيانات في الوقت الفعلي مع WebSockets

## 4. قوانين API والبيانات

- **RESTful API**: تصميم RESTful APIs مع HTTP methods الصحيحة
- **GraphQL**: استخدام GraphQL للبيانات المعقدة مع resolvers فعالة
- **التحقق**: التحقق من صحة البيانات على كل من client وserver
- **الترميز**: استخدام JSON Web Tokens للمصادقة الآمنة
- **الحد من الطلبات**: تنفيذ rate limiting و throttling

## 5. قوانين الأداء

- **Code Splitting**: تقسيم الكود إلى chunks صغيرة
- **Tree Shaking**: إزالة الكود غير المستخدم
- **CDN**: استخدام CDN للأصول الثابتة
- **Compression**: تمكين Gzip/Brotli compression
- **Caching**: استخدام browser caching headers الصحيحة

## 6. قوانين الأمان

- **HTTPS**: إجبار استخدام HTTPS في الإنتاج
- **CSP**: تطبيق Content Security Policy صارمة
- **CORS**: تكوين CORS بشكل صحيح
- **التحقق**: التحقق من صحة جميع المدخلات والمخرجات
- **التشفير**: تشفير البيانات الحساسة في الراحة والانتقال

## 7. قوانين DevOps والنشر

- **CI/CD**: إعداد pipelines CI/CD مع GitHub Actions
- **الاختبارات**: تشغيل اختبارات تلقائية قبل كل نشر
- **المراجعة**: مراجعة الكود من قبل الأقران قبل الدمج
- **النسخ**: استخدام semantic versioning (SemVer)
- **التوثيق**: تحديث الوثائق مع كل تغيير

## 8. قوانين المراقبة والتشخيص

- **السجلات**: تسجيل الأخطاء والأحداث المهمة
- **المتريكات**: تتبع أداء التطبيق واستخدام الموارد
- **الإنذارات**: إعداد إنذارات للأخطاء الحرجة
- **التحليلات**: استخدام Google Analytics أو بديل مفتوح المصدر

## 9. قوانين التوافق

- **المتصفحات**: دعم Chrome, Firefox, Safari, Edge (آخر نسختين)
- **Node.js**: دعم Node.js 16+ مع LTS
- **الأجهزة**: اختبار على أجهزة مختلفة (desktop, tablet, mobile)
- **الشبكات**: التعامل مع حالات عدم الاتصال والشبكات البطيئة

## 10. قوانين التوثيق

- **README**: README شامل مع تعليمات التثبيت والاستخدام
- **API Docs**: توثيق API كامل مع أمثلة
- **Code Comments**: تعليقات واضحة لكل وظيفة مع JSDoc
- **Changelog**: سجل تغييرات مفصل لكل إصدار

## 11. قوانين الأداء المتقدمة

- **Web Workers**: استخدام Web Workers للمهام الثقيلة
- **Service Workers**: تنفيذ PWA مع offline support
- **Virtual Scrolling**: للقوائم الطويلة
- **Memoization**: تخزين النتائج المحسوبة
- **Debouncing/Throttling**: تحسين أداء الأحداث

## 12. قوانين التجربة المستخدم

- **التحميل**: عرض loading states واضحة
- **الأخطاء**: رسائل أخطاء مفيدة وإرشادات للإصلاح
- **النجاح**: تأكيدات واضحة للإجراءات الناجحة
- **الإرشادات**: onboarding flow للمستخدمين الجدد
- **البحث**: بحث فوري مع auto-complete

## 13. قوانين البنية التحتية

- **Docker**: استخدام Docker containers للنشر
- **Kubernetes**: orchestration للتطبيقات الكبيرة
- **Load Balancing**: توزيع الحمل بين الخوادم
- **Database**: استخدام PostgreSQL أو MongoDB مع indexing مناسب
- **Redis**: استخدام Redis للكاش والجلسات

## 14. قوانين التطوير المستمر

- **Code Reviews**: مراجعة الكود من قبل فريق متعدد
- **Pair Programming**: برمجة ثنائية للمشاكل المعقدة
- **Refactoring**: إعادة هيكلة الكود بانتظام
- **Learning**: متابعة أحدث التقنيات والممارسات
- **Feedback**: جمع ملاحظات المستخدمين باستمرار

## 15. قوانين القياس

- **KPIs**: تتبع مؤشرات الأداء الرئيسية
- **User Satisfaction**: قياس رضا المستخدمين
- **Performance Budget**: تحديد ميزانية الأداء وعدم تجاوزها
- **Error Rate**: تقليل معدل الأخطاء إلى أقل من 1%
- **Uptime**: الحفاظ على uptime 99.9% أو أفضل

## ملاحظات التنفيذ

- يتم<thinking>
  The user has requested to put the best rules in the file "C:\Users\<USER>\Documents\augment-projects\youtube-player-app\.blackboxrules" to make the model perfect. This is related to configuring or improving the Blackbox AI model behavior or settings via a rules file.

I need to:

- Understand the format and typical content of a .blackboxrules file.
- Provide a set of best practice rules or configurations that optimize the model's performance.
- The file path is outside the current working directory (Desktop), but I can create or edit the file by specifying the full path.

Since the user wants the best rules to make the model perfect, I should provide a comprehensive set of rules that cover typical improvements such as:

- Instruction clarity
- Response style
- Safety and ethical guidelines
- Context handling
- Error handling
- Code style preferences
- Interaction guidelines

I do not have the current content of the .blackboxrules file, so I will create a new one with best practice rules.

I will prepare a detailed .blackboxrules file content and write it to the specified path.

Plan:

- Create a .blackboxrules file content with best practice rules for the model.
- Write the file to "C:\Users\<USER>\Documents\augment-projects\youtube-player-app\.blackboxrules"
- Confirm with the user after writing.

Dependent files: None
Followup steps: Confirm file creation success, then inform user.

</thinking>

<create_file>
<path>C:/Users/<USER>/Documents/augment-projects/youtube-player-app/.blackboxrules</path>
<content>

# Blackbox AI Rules Configuration

# This file contains best practice rules to optimize model performance and interaction quality.

# General Instructions

[General]

# Use clear, concise, and direct language.

response_style = direct

# Avoid conversational fillers such as "Great", "Certainly", "Okay", "Sure".

avoid_fillers = true

# Always provide complete and thorough answers.

complete_answers = true

# Do not end responses with questions or offers for further assistance.

no_open_ended = true

# Safety and Ethics

[Safety]

# Avoid generating harmful, offensive, or biased content.

filter_harmful_content = true

# Respect user privacy and confidentiality.

respect_privacy = true

# Context Handling

[Context]

# Use all available context to provide accurate and relevant responses.

use_full_context = true

# Avoid assumptions without sufficient context.

avoid_unfounded_assumptions = true

# Code Generation and Editing

[Code]

# Always provide complete code files when creating new files.

complete_code_files = true

# Use precise and exact matching when editing files.

exact_match_editing = true

# Follow best practices and coding standards.

follow_best_practices = true

# Provide explanations for code changes when appropriate.

explain_code_changes = true

# Interaction Guidelines

[Interaction]

# Ask follow-up questions only when necessary and keep them clear and concise.

ask_followup_only_when_needed = true

# Limit tool usage to one per message.

one_tool_per_message = true

# Performance

[Performance]

# Prioritize step-by-step problem solving.

step_by_step = true

# Confirm success of each step before proceeding.

confirm_before_next = true

# Logging and Debugging

[Debug]

# Log errors and unexpected behavior for review.

log_errors = true

# Provide actionable suggestions for error resolution.

suggest_fixes = true

# Customization

[Customization]

# Allow user to override default rules with explicit instructions.

allow_user_overrides = true

# End of .blackboxrules
